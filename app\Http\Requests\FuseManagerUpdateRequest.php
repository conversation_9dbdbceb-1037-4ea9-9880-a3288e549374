<?php

namespace App\Http\Requests;

use App\Models\FuseManager;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class FuseManagerUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [];
        return  array_merge($rules, [
            'create_revision' => ['boolean'], //Flag is trying to create a new revision now
            //Able to save same name as long as revision is different
            'name' => ['string', 'max:255', Rule::unique(FuseManager::class)->ignore($this->fuse_manager)->where('revision', $this->input('revision'))->whereNull('deleted_at')],
            'product_type_id' => ['numeric', 'max:255'],
            'product_id' => ['numeric', 'max:255'],
            'product_group_id' => ['required', 'numeric', 'max:255'],
            'bin_matrix_id' => ['required', 'numeric', 'max:255'],
        ]);
    }
}
