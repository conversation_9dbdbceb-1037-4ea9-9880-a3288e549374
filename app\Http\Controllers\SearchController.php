<?php

namespace App\Http\Controllers;

use App\Models\BinMatrix;
use App\Models\BinMatrixFlowAttribute;
use App\Models\FuseManager;
use App\Models\FuseManagerAttribute;
use App\Models\LineitemManager;
use Illuminate\Http\Request;


/**
 * Place all re-usable search function here
 */
class SearchController extends Controller
{
    public function getBinMatrixOptions(Request $request)
    {
        $matrices = BinMatrix::whereProducts($request->product_type_id, $request->product_id, $request->product_group_id)->get();
        $data = [];
        foreach ($matrices as $item) {
            $data[$item->id] = $item->name . ' - ' . $item->revision;
        }

        return response()->json($data);
    }

    public function getLineitemFamilyOptions(Request $request)
    {
        $single = LineitemManager::where('id', $request->original_id)->whereNotNull('generated_at')->select('id', 'name', 'revision')->first()->toArray();
        $multi = LineitemManager::where('original_id', $request->original_id)->whereNotNull('generated_at')->select('id', 'name', 'revision')->get()->toArray();
        //append $single to first array
        array_unshift($multi, $single);

        $data = [];
        foreach ($multi as $item) {
            $data[$item['generate_table_name']] = $item['name'] . ' - ' . $item['revision'];
        }

        return response()->json($data);
    }

    /**
     * Get Attributes Selector Options Base on module
     */
    public function getAttributesSelectorOptions(Request $request)
    {
        $fuse_manager_id = $request->fuse_manager_id;
        //Use in Fuse Manager's Header Attributes Selector
        if ($fuse_manager_id != null) {
            $fm = FuseManager::find($fuse_manager_id);
            $bin_matrix_id = $fm->bin_matrix_id;
        } else {
            //Use in bin matrix Flow Attributes Selector
            $bin_matrix_id = $request->bin_matrix_id;
        }

        $bin_matrix = BinMatrix::with('speedBatch.lineitemManagers.lineitemAttributes')->find($bin_matrix_id);
        $available_modules = config('settings.modules');

        $data['modules'] = config('settings.base_module_selections');
        $data['left'] = [];
        $data['right'] = [];

        if ($bin_matrix == null || $bin_matrix->speedBatch == null) return $data;

        $speed = $bin_matrix->speedBatch->getBasedModal();
        //Get all columns from speed table in alphabetical order
        $data['left']['Speed'] = collect($speed->getUsableColumnsWithId())
            ->sortBy(fn($item) => strtolower($item['attribute']))
            ->values()->all();

        foreach ($bin_matrix->speedBatch->lineitemManagers as $lineitemManager) {
            foreach ($lineitemManager->lineitemAttributes as $lineitemAttribute) {
                $module = $lineitemManager->module;
                if (!isset($data['modules'][$module]) && in_array($module, $available_modules)) {
                    $data['modules'][$module] = $module;
                }
                $data['left'][$module][] = [
                    'id' => $lineitemAttribute->id,
                    'attribute' => $lineitemAttribute->attribute,
                ];
            }
        }

        if ($fuse_manager_id != null) {
            $rightColumns = FuseManagerAttribute::where('fuse_manager_id', $fuse_manager_id)->select('id', 'name', 'sequence', 'attribute_name', 'module')->orderBy('sequence')->get();
            $data['right'] = $rightColumns;
        } else {
            $rightColumns = BinMatrixFlowAttribute::where('bin_matrix_id', $bin_matrix_id)->select('id', 'name', 'sequence', 'attribute_name', 'module')->orderBy('sequence')->get();
            $data['right'] = $rightColumns;
        }

        //Frontend will remove leftColumn that exists in rightColumn

        return response()->json($data);
    }
}
