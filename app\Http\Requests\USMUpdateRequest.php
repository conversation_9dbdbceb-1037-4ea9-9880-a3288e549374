<?php

namespace App\Http\Requests;

use App\Models\USM;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class USMUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [];
        return  array_merge($rules, [
            'create_revision' => ['boolean'], //<PERSON> is trying to create a new revision now
            //Able to save same name as long as revision is different
            'name' => ['string', 'max:255', Rule::unique(USM::class)->ignore($this->usm)->where('revision', $this->input('revision'))->whereNull('deleted_at')],
            'product_type_id' => ['numeric', 'max:255'],
            'product_id' => ['numeric', 'max:255'],
            'product_group_id' => ['numeric', 'max:255'],
            'bin_matrix_id' => ['numeric', 'max:255'],
            'active' => ['boolean'],
            'modules' => ['array'],
            'scenarios' => ['array'],
            'fuse_manager_ids' => ['array'],
        ]);
    }
}
