<?php

namespace App\Imports;

use App\Models\SpeedDynamic;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class SpeedImport implements ToCollection, WithHeadingRow
{

    protected $columns = [];
    protected $newColumns = [];
    protected $table = null;
    protected $startImport = false;
    protected $speed_batch_id = null;

    public function __construct($table, $startImport = false, $speed_batch_id = null)
    {
        $this->table = $table;
        $this->startImport = $startImport;
        $this->speed_batch_id = $speed_batch_id;
    }

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        //Skip column
        $exclude_columns = config('settings.exclude_columns');

        // Get first row keys (headers), filtering out excluded and unnamed (numeric) headers
        $rawHeaders = array_keys($collection->first()->toArray() ?? []);
        $validHeaders = array_filter($rawHeaders, fn($key) => !is_numeric($key) && !in_array($key, $exclude_columns));
        $this->columns = array_values($validHeaders);

        // Check if the header row is valid
        $missing_columns = $this->getMissingCompulsoryHeaders($this->columns);
        if (count($missing_columns) > 0) {
            throw new \Exception("File used is not valid. Missing compulsory columns: " . implode(', ', $missing_columns));
        }

        $speedColumns = $this->getModel()->getTableColumns();

        // Check if all columns are present in the table
        $this->newColumns = array_values(array_diff($this->columns, $speedColumns));

        //After customer confirm the new columns, then start import
        if ($this->startImport) {
            \DB::statement('TRUNCATE ' . $this->table . ' RESTART IDENTITY');

            if (count($this->newColumns) > 0) {
                //Add new columns to the table
                //$this->addNewColumnsToTable();
            }

            foreach ($collection as $row) {
                $data = [];
                foreach ($row as $column => $value) {
                    if (in_array($column, $exclude_columns)) continue;
                    if (is_numeric($column)) continue;
                    $data[$column] = notEmptyOrZero($value) ? $value : null;
                }

                $model = $this->getModel();
                $model->forceCreate($data);
            }
        }
    }

    public function headingRow(): int
    {
        return 1;
    }

    /**
     * Get the extracted columns.
     */
    public function getNewColumns()
    {
        return $this->newColumns;
    }

    public function getColumns()
    {
        return $this->columns;
    }

    private function getModel()
    {
        $model = new SpeedDynamic;
        $model->setTable($this->table);
        return $model;
    }

    private function getCompulsoryHeaders()
    {
        return [
            'QDF/SSPEC',
            //'MM#',
            'PRODUCT VARIANT',
            'POWER GRADE',
            'Product Grade',
            'CORE SPEED RATIO',
            //'TRANSCEIVER SPEED RATIO',
            //'TRANSCEIVER COUNT',
            //'Spec Code',
            'Pincount',
            //'Sample/Production Type',
        ];
    }

    private function getMissingCompulsoryHeaders(array $headers)
    {
        $headersUpper = array_map('strtoupper', $headers);
        $compulsoryHeadersUpper = array_map('strtoupper', $this->getCompulsoryHeaders());
        $missing = [];
        foreach ($compulsoryHeadersUpper as $key) {
            if (!in_array($key, $headersUpper)) {
                $missing[] = $key;
            }
        }
        return $missing;
    }
}
