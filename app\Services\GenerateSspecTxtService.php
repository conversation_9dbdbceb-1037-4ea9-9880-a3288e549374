<?php

namespace App\Services;

use App\Helpers\FuseTextFile;
use App\Models\FuseConfig;

class GenerateSspecTxtService
{
    public $fuse_config_id;
    protected $fuseConfig;
    protected $binMatrix;
    protected $available_crossship = [];
    protected $lineDatabase;
    protected $lines;

    public function __construct(int $fuse_config_id)
    {
        $this->fuse_config_id = $fuse_config_id;
        $this->fuseConfig = FuseConfig::with('fuseConfigItems', 'fuseConfigItems.fuseConfigItemAddresses', 'fuseManager.binMatrix.binMatrixItems.binMatrixFlows')->findOrFail($fuse_config_id);
        $this->binMatrix = $this->fuseConfig->fuseManager->binMatrix;

        $this->lineDatabase = new LineDatabase($this->binMatrix->speed_batch_id);
        $this->lines = $this->lineDatabase->getModel()->get()->keyBy('QDF/SSPEC');
    }

    /**
     * Generate the Sspec export XML file
     * 
     * @return string The XML document as a string
     */
    public function generate()
    {
        $content = FuseTextFile::header($this->fuseConfig);

        $line = new LineDatabase($this->binMatrix->speed_batch_id);
        $columns = $line->getSspecXmlColumns();
        $rows = $line->getModel()->get();

        // First determine the maximum length for each column
        $maxLengths = [];
        foreach ($columns as $column) {
            $maxLengths[$column] = strlen($column);
        }

        // Check data to find maximum length for each column
        foreach ($rows as $row) {
            foreach ($columns as $column) {
                $value = $row->{$column} ?? '';
                $maxLengths[$column] = max($maxLengths[$column], strlen($value));
            }
        }

        // Calculate max prefix length (F[MM#] == )
        $maxPrefixLength = 0;
        foreach ($rows as $row) {
            $mm = $row->{'MM#'} ?? '';
            $prefixLength = strlen("F" . $mm . " == ");
            $maxPrefixLength = max($maxPrefixLength, $prefixLength);
        }

        // Create header with proper spacing
        $headerLine = "# " . str_repeat(" ", $maxPrefixLength);
        $columnCount = count($columns);

        // Add each column to header with proper spacing
        for ($i = 0; $i < $columnCount; $i++) {
            $column = $columns[$i];
            $headerLine .= $column;

            // Add comma right after the column name, then spaces to align next column
            if ($i < $columnCount - 1) {
                $headerLine .= ",";
                // Add padding based on max length
                $padding = $maxLengths[$column] - strlen($column) + 1; // +1 for the space after comma
                $headerLine .= str_repeat(" ", $padding);
            }
        }

        $content .= $headerLine . "\n";

        // Create data lines with proper alignment
        foreach ($rows as $row) {
            $mm = $row->{'MM#'} ?? '';
            $prefix = "F" . $mm . " == ";
            $prefixPadding = str_repeat(" ", $maxPrefixLength - strlen($prefix));

            $line = "# " . $prefix;

            // Add each column data with proper spacing
            for ($i = 0; $i < $columnCount; $i++) {
                $column = $columns[$i];
                $value = $row->{$column} ?? '';

                // If first column, need to account for prefix padding
                if ($i == 0) {
                    $line .= $prefixPadding . $value;
                } else {
                    $line .= $value;
                }

                // Add comma immediately after value, then spaces for padding
                if ($i < $columnCount - 1) {
                    $line .= ",";
                    // Add padding based on max length
                    $padding = $maxLengths[$column] - strlen($value) + 1; // +1 for the space after comma
                    $line .= str_repeat(" ", $padding);
                }
            }

            $content .= $line . "\n";
        }

        // Add a separator based on the maximum line length
        $maxLineLength = 2 + $maxPrefixLength; // Start with "# " plus max prefix length
        foreach ($columns as $index => $column) {
            $maxLineLength += $maxLengths[$column];
            if ($index < $columnCount - 1) {
                $maxLineLength += 2; // ", "
            }
        }
        $content .= "\n# " . str_repeat("#", $maxLineLength) . "\n";

        $content .= $this->loadBinMatrixBomList();

        foreach ($this->fuseConfig->fuseConfigItems as $item) {
            $binMaxLength = 0;
            foreach ($this->available_crossship as $crossShip) {
                $binMaxLength = max($binMaxLength, strlen($crossShip));
            }

            $content .= "# Register: {$item->register}\n";
            //item->size is the number of bits

            // Get the size from the item
            $size = $item->size;

            // Calculate the left padding to align with "n: " position
            // First, get the length of a sample crossship (use the first one)
            $sampleCrossShip = $this->available_crossship[0] ?? '';

            // Calculate the exact left padding - this will align with the fuse data
            // Format: "FUSEDATA:   {$item->register}: {$crossShip}: n: "
            $paddingLength = strlen("FUSEDATA:   " . $item->register . ": " . $sampleCrossShip . ": n: ");
            $leftPadding = str_repeat(" ", $paddingLength - 1);

            // Determine the maximum number of digits in the size
            $maxDigits = strlen((string)($size - 1));

            // For each digit position (thousands, hundreds, tens, ones)
            for ($digit = $maxDigits - 1; $digit >= 0; $digit--) {
                $content .= "#" . $leftPadding;

                // For each bit position
                for ($i = $size - 1; $i >= 0; $i--) {
                    // Get the digit at the current position
                    $divisor = pow(10, $digit);
                    $value = floor(($i / $divisor) % 10);

                    // Only show zeros on the units digit (digit 0)
                    if ($value == 0 && $digit > 0 && $i < pow(10, $digit)) {
                        $content .= " ";
                    } else {
                        if ($item->datatype == "Hex") {
                            $value = $value . " ";
                        }
                        $content .= $value;
                    }
                }

                $content .= "\n";
            }
            $content .= "\n";
            $content .= "Visibility: {$item->register}: ";
            $content .= str_repeat(" ", $binMaxLength + 5);

            if ($item->datatype == "Hex") {
                $content .= str_repeat("0", $item->size * 2) . "\n";
            } else {
                $content .= str_repeat("0", $item->size) . "\n";
            }
            foreach ($this->available_crossship as $crossShip) {
                $content .= "FUSEDATA:   ";
                $content .= "{$item->register}: ";
                $content .= $crossShip . ": ";
                $content .= "n: ";

                //Reverse sort by start_address
                $item->fuseConfigItemAddresses = $item->fuseConfigItemAddresses->sortBy('start_address', SORT_REGULAR, true);
                foreach ($item->fuseConfigItemAddresses as $address) {
                    //Conditional does not require repeat
                    if ($address->data_map_type == FUSE_CONDITIONAL) {
                        //If is Conditional use parser
                        if (empty($address->expression)) {
                            //If empty expression show space, easier to detect the issue
                            for ($i = 0; $i < $address->size; $i++) {
                                $content .= " ";
                            }
                        } else {
                            $parser = new RowParser;
                            $parser->addExpression("parse_result", $address->expression);
                            $result = $parser->parseRow($this->lines[$crossShip]->toArray());
                            $value = $result['parse_result'];
                        }
                        //Convert to hex
                        if ($item->datatype == "Hex" && notEmptyOrZero($value)) {
                            $value = binToHexWithPack($value);
                        }

                        $content .= $value;
                    } else {
                        for ($i = 0; $i < $address->size; $i++) {
                            //Get based
                            $value = $address->de_rv_xd;

                            if ($address->data_map_type == FUSE_DIRECT) {
                                //If is direct get the column
                                $column = str_replace('[', '', str_replace(']', '', $address->de_rv_xd));
                                $value = $this->lines[$crossShip]->{$column};
                                if (!notEmptyOrZero($value)) {
                                    $value = " "; //show space, easier to detect the issue
                                }
                            }
                            $content .= $value;
                        }
                    }
                }
                $content .= "\n";
            }

            $content .= "\n";
        }

        $content .= $this->repeatCrossShip();

        return $content;
    }

    private function loadBinMatrixBomList()
    {
        $content = "#    QDF/SSPEC\n";

        $bomList = [];
        $binMatrixItems = $this->binMatrix->binMatrixItems;
        foreach ($binMatrixItems as $binMatrixItem) {
            foreach ($binMatrixItem->binMatrixFlows as $binMatrixFlow) {
                foreach ($binMatrixFlow->crossShipExplode as $crossShip) {
                    if (!isset($bomList[$crossShip])) {
                        $bomList[$crossShip] = [];
                        $this->available_crossship[] = $crossShip;
                    }

                    foreach ($binMatrixItem->bomListExplode as $bom) {
                        $bomList[$crossShip][] = $bom . "_" . $binMatrixFlow->pass_bin;
                    }
                }
            }
        }

        // Calculate alignment to match "QDF/SSPEC" position
        // Header: "#    QDF/SSPEC"
        // LDB lines: "LDB: {$crossShip}:"
        // Need to align what comes after "LDB: {$crossShip}:" with "QDF/SSPEC" position (which is at position 5)
        foreach ($bomList as $crossShip => $boms) {
            $prefix = "LDB: {$crossShip}:";
            // Use fixed padding of 6 spaces for consistent alignment
            $padding = 6;

            $content .= $prefix;
            $content .= str_repeat(" ", $padding);
            $content .= implode(',', $boms);
            $content .= "\n";
        }

        $content .= "\n";
        return $content;
    }

    private function repeatCrossShip()
    {
        $content = "";
        foreach ($this->available_crossship as $crossShip) {
            $content .= "QDF_SSPEC_DEF: {$crossShip}: {$crossShip}\n";
        }
        return $content;
    }

    public function getFileName(): string
    {
        return "sspec.txt";
    }

    public function generateTxt()
    {
        $content = $this->generate();
        $headers = [
            'Content-Type' => 'text/plain',
            'Content-Disposition' => "attachment; filename={$this->getFileName()}",
        ];
        return response($content, 200, $headers);
    }
}
