<script setup>
import { ref, onMounted } from 'vue';
import axios from 'axios';
import Flash<PERSON>lert from '@/Components/FlashAlert.vue'

const props = defineProps({
  batchId: {
    type: Number,
    required: true
  }
});

const routeGroupName = 'speedbatch';
const columns = ref([]);
const flash = ref({ show: false, status: 'success', message: '' });

onMounted(async () => {
  try {
    const res = await axios.get(route(routeGroupName + '.format.get', props.batchId)) 
    const originalColumns = res.data

    columns.value = Object.entries(originalColumns).map(([name, type]) => ({
      name,
      type
    }))
  } 
  catch (error) {
    console.error('Failed to load columns:', error)
    flash.value = { show: true, status: 'danger', message: 'Failed to load columns.' }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
})

const submit = async () => {
  try {
    const res = await axios.post(route(routeGroupName + '.format.post', props.batchId), {
      columns: columns.value
    });
    flash.value = { show: true, status: 'success', message: res.data.message || 'Column types updated successfully!' }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  } catch (error) {
    console.error('Failed to update column types:', error)
    let msg = 'Column types update failed.'
    if (error.response && error.response.data && error.response.data.error) {
      msg = error.response.data.error
    }
    flash.value = { show: true, status: 'danger', message: msg }
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
}
</script>

<template>
  <div>
    <FlashAlert
      v-if="flash.show"
      :status="flash.status"
      @close="flash.show = false"
      class="mb-2"
    >
      {{ flash.message }}
    </FlashAlert>
    <form @submit.prevent="submit">
      <table class="table table-bordered"> 
        <thead>
          <tr>
            <th>Column Name</th>
            <th>Data Type</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(col, index) in columns" :key="index">
            <td>
              <input
                type="text"
                class="form-control"
                v-model="col.name"
                readonly
              />
            </td>
            <td>
              <select class="form-select" v-model="col.type" required>
                <option value="String">String</option>
                <option value="Integer">Integer</option>
                <option value="Float/Exponential">Float</option>
              </select>
            </td>
          </tr>
        </tbody>
      </table>

      <button type="submit" class="btn btn-primary mt-2"> Save </button>
    </form>
  </div>
</template>


