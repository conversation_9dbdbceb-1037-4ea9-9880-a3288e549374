<script setup>
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import HeadRow from '@/Components/Table/HeadRow.vue';
import Paginate from '@/Components/Table/Paginate.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, router, useForm, Link } from '@inertiajs/vue3';
import { ref } from 'vue';
import { formatDate } from '@/helper';
import ImportModalComponentType from '@/Components/ImportModalComponentType.vue';

const props = defineProps({
    header: {
        type: Object,
    },
    filters: {
        type: Object,
    },
    list: {
        type: Object,
        default: () => ({}),
    },
    sourceTypes: {
        type: Object,
        default: () => ({}),
    },
    files: {
        type: Object,
        default: () => ({}),
    },
});

const routeGroupName = 'component_types';
const headerTitle = ref('Component Types');
const form = useForm(props.filters);

// Import modal properties
const importUrl = ref('');
const importTitle = ref('');
const fileTypes = ref([]);

const setImportType = type => {
    if (type === 'complex') {
        importUrl.value = route(routeGroupName + '.import.complex_type');
        importTitle.value = 'Import Complex Types';
        fileTypes.value = 'complexTypes';
    } else if (type === 'details') {
        importUrl.value = route(routeGroupName + '.import.details');
        importTitle.value = 'Import Details';
        fileTypes.value = 'upload';
    }
};

const sort = field => {
    form.sort.field = field;
    form.sort.direction = form.sort.direction == '' || form.sort.direction == 'desc' ? 'asc' : 'desc';
    submit();
};

const submit = () => {
    form.get(route(routeGroupName + '.index'), {
        preserveScroll: true,
    });
};

const clearFilters = () => {
    form.keyword = '';
    submit();
}

const destroy = (id, name) => {
    const c = confirm(`Delete this component type ${name} ?`);
    if (c) {
        router.delete(route(routeGroupName + '.destroy', id));
    }
};
</script>

<template>
    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <template #header>
            {{ headerTitle }}
        </template>

        <template #h-buttons>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#importModal" @click="setImportType('complex')">
                <i class="bi bi-filetype-xml"></i>
                Import Complex Types
            </button>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#importModal" @click="setImportType('details')">
                <i class="bi bi-filetype-xml"></i>
                Import Details
            </button>
            <Link class="btn btn-primary" :href="route(routeGroupName + '.create')">
                <i class="bi bi-plus"></i>
                Create
            </Link>
        </template>

        <div class="my-3 p-3 bg-body rounded shadow-sm">
            <form @submit.prevent="submit">
                <div class="row mb-3 align-items-center g-3">
                    <div class="col-md-2">
                        <div class="form-floating">
                            <input v-model="form.keyword" type="text" class="form-control" id="keywordInput" placeholder="Keyword" autocomplete="off" />
                            <label for="keywordInput">Keyword</label>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <PrimaryButton type="submit" class="mx-2" :disabled="form.processing">
                            <i class="bi bi-search"></i>
                            Search
                        </PrimaryButton>
                        <SecondaryButton type="button" @click="clearFilters" :disabled="form.processing">
                            <i class="bi bi-x-octagon"></i>
                            Clear
                        </SecondaryButton>
                    </div>
                </div>
            </form>

            <table class="table table-bordered table-striped table-hover">
                <thead>
                    <tr>
                        <HeadRow>Actions</HeadRow>
                        <HeadRow v-for="head in header" :key="head" :field="head.field" :sort="head.sortable ? filters.sort : null" @sortEvent="sort" :disabled="form.processing">{{ head.title }}</HeadRow>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(item, index) in list.data" :key="index">
                        <td width="10%">
                            <Link :href="route(routeGroupName + '.show', item.id)" class="btn btn-sm btn-link">
                                <i class="bi bi-book-half"></i>
                            </Link>
                            <Link v-show="item.source!='component'" :href="route(routeGroupName + '.edit', item.id)" class="btn btn-sm btn-link">
                                <i class="bi bi-pencil"></i>
                            </Link>
                            <!-- <button @click="destroy(item.id, item.name)" class="btn btn-sm btn-link">
                                <i class="bi bi-trash"></i>
                            </button> -->
                        </td>
                        <td>{{ item.name }}</td>
                        <td>{{ item.level }}</td>
                        <td>{{ item.type }}</td>
                        <td>{{ formatDate(item.created_at) }}</td>
                    </tr>
                </tbody>
            </table>
            <Paginate :data="list" />
        </div>

        <ImportModalComponentType :url="importUrl" :title="importTitle" :sourceTypes="sourceTypes" :files="files" :fileTypes="fileTypes"></ImportModalComponentType>
    </AuthenticatedLayout>
</template>
