<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, Link, usePage } from '@inertiajs/vue3';
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import Select from '@/Components/Select.vue';
import BomListAttributesModal from './BomListAttributesModal.vue';
import BomListExpressionsModal from './BomListExpressionsModal.vue';
import FlowModal from './FlowModal.vue';
import FlowAttributeSelectorModal from './FlowAttributeSelectorModal.vue';
import axios from 'axios';
import ProductSelectors from '@/Components/ProductSelectors.vue';
import FloatingScrollButtons from '@/Components/FloatingScrollButtons.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    productTypes: {
        type: Object,
    },
    products: {
        type: Object,
    },
    productGroups: {
        type: Object,
    },
    binTypes: {
        type: [Array, Object],
    },
    createRevision: {
        type: Boolean,
    },
    sspec_list: {
        type: Array,
    },
    states: {
        type: Array,
    },
    speedBatches: {
        type: Array,
    },
});

const routeGroupName = 'bin_matrix';
const headerTitle = ref('Bin Matrix');
const selectedBinMatrixItemId = ref(null);
const selectedBinMatrixItem = ref(null);

const localData = ref({ ...props.data });

// Function to initialize tooltips
const initTooltips = () => {
    // Dispose any existing tooltips first to prevent duplicates
    const existingTooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    existingTooltips.forEach(el => {
        const tooltip = bootstrap.Tooltip.getInstance(el);
        if (tooltip) {
            tooltip.dispose();
        }
    });

    // Initialize tooltips
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
};

onMounted(() => {
    // Initialize tooltips when component is mounted
    initTooltips();
});

watch(
    () => props.data,
    newVal => {
        localData.value = { ...newVal };
    },
);

const form = useForm({
    create_revision: props.createRevision,
    revision: props.data.revision,
    name: props.data.name ?? null,
    active: props.data.active ?? true,
    product_type_id: props.data.product_type_id ?? null,
    product_id: props.data.product_id ?? null,
    product_group_id: props.data.product_group_id ?? null,
    speed_batch_id: props.data.speed_batch_id ?? null,
    bin_matrix_items: props.data.bin_matrix_items ?? [],
});

// Watch for changes to bin_matrix_items array
watch(
    () => form.bin_matrix_items,
    () => {
        // Wait for the DOM to update before reinitializing tooltips
        nextTick(() => {
            initTooltips();
        });
    },
    { deep: true } // Watch for nested changes in the array
);

const onBomListSuccess = (successMsg) => {
    // Show success message if provided, manually overwrite the flash message
    if (successMsg != null) {
        usePage().props.flash.message = successMsg;
    }

    fetchData();
};

const onFlowSuccess = (successMsg) => {
    //Reset so the Modal can refresh
    selectedBinMatrixItemId.value = null;
    selectedBinMatrixItem.value = null;
};

const fetchData = () => {
    // Refresh data from the server
    axios.get(route('bin_matrix.data', props.data.id))
        .then(response => {
            localData.value = response.data.data;
            // Update the form with the latest data
            form.bin_matrix_items = localData.value.bin_matrix_items;

            // Re-initialize tooltips after data update
            nextTick(() => {
                initTooltips();
            });
        })
        .catch(error => {
            console.error('Error fetching data:', error);
        });
};

const addRow = () => {
    form.bin_matrix_items.push({
        id: null,
        bom_group: '',
        bin_type: '',
        bom_list: '',
        sequence: form.bin_matrix_items.length + 1,
    });

    // Wait for the DOM to update before reinitializing tooltips
    nextTick(() => {
        initTooltips();
    });
};

const removeRow = index => {
    form.bin_matrix_items.splice(index, 1);
    //Reupdate the sequence
    form.bin_matrix_items.forEach((row, index) => {
        row.sequence = index + 1;
    });

    // Wait for the DOM to update before reinitializing tooltips
    nextTick(() => {
        initTooltips();
    });
};

const assignFlowModal = row => {
    selectedBinMatrixItemId.value = row.id;
    selectedBinMatrixItem.value = row;
};

const setState = state => {
    const c = confirm('Change the state to ' + state);
    if (c) {
        axios
            .post(route('bin_matrix.state.update', props.data.id), { state: state })
            .then(response => {
                localData.value.state = state;
                alert(response.data.message);
            })
            .catch(error => {
                alert(error.response.data.message);
            });
    }
};
// Computed property to filter speed batches by the selected product group
const filteredSpeedBatches = computed(() => {
    if (!form.product_group_id) {
        return []; // Return empty array if no product group selected
    }

    // Filter speed batches to only show those matching the selected product group
    return props.speedBatches.filter(batch => batch.product_group_id === parseInt(form.product_group_id));
});

const isCreateRevision = computed(() => {
    return props.createRevision ? true : false;
});

const editDisabled = computed(() => {
    return localData.value.state == 'SNAPSHOT';
});

const submitForm = () => {
    const options = {
        preserveScroll: true,
        onSuccess: () => {
            // Refresh data from the server after successful save
            fetchData();
        }
    };

    if (props.data.id == null) {
        form.post(route(routeGroupName + '.store'), options);
    } else {
        form.patch(route(routeGroupName + '.update', props.data.id), options);
    }
};
</script>

<template>

    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <template #header> {{ headerTitle }} </template>
        <span v-if="isCreateRevision">(New Revision {{ props.data.revision }})</span>

        <form @submit.prevent="submitForm">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <ul class="nav nav-tabs card-header-tabs">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#tab_1">Details</a>
                        </li>
                    </ul>
                    <div v-if="data.id != null && isCreateRevision == false" class="d-flex gap-2">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false"><i class="bi bi-check-circle"></i> {{ localData.state }}</button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li v-for="state in states" :key="state">
                                    <button type="button" class="dropdown-item" @click="setState(state)"
                                        :disabled="state == localData.state">{{ state }}</button>
                                </li>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false"><i class="bi bi-three-dots-vertical"></i> More</button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li>
                                    <button type="button" class="dropdown-item" data-bs-toggle="modal"
                                        data-bs-target="#bomListAttributesModal">Bom List Fields</button>
                                </li>
                                <li>
                                    <button type="button" class="dropdown-item" data-bs-toggle="modal"
                                        data-bs-target="#bomListExpressionsModal">Bom List Expressions</button>
                                </li>
                                <li>
                                    <button type="button" class="dropdown-item" data-bs-toggle="modal"
                                        data-bs-target="#flowAttributeSelectorModal">Flow Attributes Selector</button>
                                </li>
                                <li>
                                    <hr class="dropdown-divider" />
                                </li>
                                <li>
                                    <Link class="dropdown-item"
                                        :href="route(routeGroupName + '.revision.create', data.id)">
                                    Create New Revision</Link>
                                </li>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                <i class="bi bi-file-earmark-arrow-down"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li>
                                    <a class="dropdown-item"
                                        :href="route(routeGroupName + '.export_binmatrix_xml', data.id)">Export
                                        BinMatrix XML</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane fade pt-10 show active" id="tab_1" role="tabpanel" aria-labelledby="tab_1">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <InputLabel for="name" value="Name" />
                                    <TextInput id="name" type="text" v-model="form.name" :invalid="form.errors.name"
                                        required />
                                    <InputError :message="form.errors.name" />
                                </div>

                                <ProductSelectors v-model="form" :product-types="productTypes" :products="products"
                                    :product-groups="productGroups" :errors="form.errors" />

                                <div class="col-md-3">
                                    <InputLabel for="speed_batch_id" value="Speed" />
                                    <Select id="speed_batch_id" v-model="form.speed_batch_id"
                                        :invalid="form.errors.speed_batch_id" :options="filteredSpeedBatches"
                                        :placeholder="'Select Speed Batch'" :label_key="'label'" required />
                                    <InputError :message="form.errors.speed_batch_id" />
                                </div>

                                <div class="col-12">
                                    <div class="text-end">
                                        Revision: {{ data.revision }} <br />
                                        <template v-if="data.created_user">Created By: {{ data.created_user.name }}
                                        </template>
                                    </div>
                                </div>
                            </div>

                            <PrimaryButton type="submit" v-if="!editDisabled && data.id != null"
                                v-html="data.id == null ? 'Create' : 'Save'" :disabled="form.processing">
                            </PrimaryButton>

                            <div class="row g-3 mt-3">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th width="3%">
                                                <button type="button" class="btn btn-sm btn-primary m-1" @click="addRow"
                                                    title="Add Bom Group"><i class="bi bi-plus"></i></button>
                                            </th>
                                            <th width="3%">No</th>
                                            <th width="15%">Bom Group</th>
                                            <th width="10%">Bin Type</th>
                                            <th>Bom List</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(row, index) in form.bin_matrix_items" :key="index">
                                            <td class="text-center">
                                                <button type="button" class="btn btn-sm btn-danger m-1"
                                                    @click="removeRow(index)" title="Remove"><i
                                                        class="bi bi-dash"></i></button>
                                                <div v-if="row.id == null" data-bs-toggle="tooltip"
                                                    data-bs-title="Save First">
                                                    <button type="button" class="btn btn-sm btn-warning m-1"
                                                        :disabled="row.id == null"><i class="bi bi-water"></i></button>
                                                </div>
                                                <button v-else type="button" class="btn btn-sm btn-warning m-1"
                                                    @click="assignFlowModal(row)" title="Flow"
                                                    :disabled="row.id == null" data-bs-toggle="modal"
                                                    data-bs-target="#flowModal"><i class="bi bi-water"></i></button>
                                            </td>
                                            <td class="text-center">{{ index + 1 }}</td>
                                            <td>
                                                <TextInput type="text"
                                                    v-model="form.bin_matrix_items[index].bom_group" />
                                            </td>
                                            <td>
                                                <TextInput type="text"
                                                    v-model="form.bin_matrix_items[index].bin_type" />
                                            </td>
                                            <td>
                                                <textarea class="form-control"
                                                    v-model="form.bin_matrix_items[index].bom_list" readonly></textarea>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex">
                        <div class="me-auto">
                            <Link class="btn btn-secondary me-2" :href="route(routeGroupName + '.index')">Back</Link>
                            <PrimaryButton type="submit" v-if="!editDisabled"
                                v-html="data.id == null ? 'Create' : 'Save'" :disabled="form.processing">
                            </PrimaryButton>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <BomListAttributesModal :id="data.id" :editDisabled="editDisabled" />
        <BomListExpressionsModal :id="data.id" :editDisabled="editDisabled" @saveSuccess="onBomListSuccess" />
        <FlowAttributeSelectorModal :bin_matrix_id="data.id" :editDisabled="editDisabled"
            @saveSuccess="onFlowSuccess" />
        <FlowModal :bin_matrix_item_id="selectedBinMatrixItemId" :item="selectedBinMatrixItem" :sspec_list="sspec_list"
            :editDisabled="editDisabled" />

        <FloatingScrollButtons />
    </AuthenticatedLayout>
</template>
