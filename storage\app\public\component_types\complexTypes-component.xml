<?xml version="1.0" encoding="utf-8"?>
<UpsTypes xmlns="urn:xmlns:ups">
  <UpsType level="Package" config_name="StandardPkg_non_scenario" type="StandardPkg" scenarioEnabled="false">
  </UpsType>

  <UpsType level="Rail" config_name="ConditionComponent_non_scenario" type="ConditionComponent" scenarioEnabled="false">
  </UpsType>
  
  <UpsType level="Block" config_name="StandardBlock_non_scenario_speed_only" type="StandardBlock" scenarioEnabled="false">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_standard" type="StandardBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model"/>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_ITD" type="StandardBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_ITD-2slope" type="StandardBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_sicc-const" type="StandardBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccConstant" description="Sicc constant model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_sicc-const-itd" type="StandardBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccConstant" description="Sicc constant model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_sicc-const-itd-2slope" type="StandardBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccConstant" description="Sicc constant model"/>
  </UpsType>
  
  <UpsType level="Block" config_name="StandardBlock_speed-discrete_sicc-const" type="StandardBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccConstant" description="Sicc constant model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_speed-discrete_sicc-const-itd" type="StandardBlock">
    <Speed type="SpeedDiscreteModel" description="Speed piecewise linear model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccConstant" description="Sicc constant model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_speed-discrete" type="StandardBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_speed-discrete-itd" type="StandardBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_speed-const_dicc-cvf-model" type="StandardBlock">
    <Speed type="SpeedConstant" description="Speed constant model" />
    <Dicc type="DiccCVFModel" description="Dicc cvf model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_speed-const_dicc-conduct" type="StandardBlock">
    <Speed type="SpeedConstant" description="Speed constant model" />
    <Dicc type="DiccConductance" description="Dicc conductance*v model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_speed-avx" type="StandardBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model" />
    <Dicc type="DiccCVFModel" description="Dicc conductance*v model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_speed-avx-itd" type="StandardBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc conductance*v model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="StandardBlock_speed-avx-itd-2slope" type="StandardBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc conductance*v model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>
  
  
  <UpsType level="Block" config_name="FivrBlock_standard" type="FivrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model"/>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="StdFivrLossModel" description="This is standard fivr loss model"/>
  </UpsType>

  <UpsType level="Block" config_name="FivrBlock_ITD" type="FivrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="StdFivrLossModel" description="This is standard fivr loss model"/>
  </UpsType>

  <UpsType level="Block" config_name="FivrBlock_ITD-2slope" type="FivrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="StdFivrLossModel" description="This is standard fivr loss model"/>
  </UpsType>

  <UpsType level="Block" config_name="FivrBlock_AVX_ITD" type="FivrBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="StdFivrLossModel" description="This is standard fivr loss model"/>
  </UpsType>

  <UpsType level="Block" config_name="FivrBlock_AVX_ITD-2slope" type="FivrBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="StdFivrLossModel" description="This is standard fivr loss model"/>
  </UpsType>

  <UpsType level="Block" config_name="FivrBlock_speed-discrete_sicc-const" type="FivrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccConstant" description="Sicc constant model"/>
    <Loss type="StdFivrLossModel" description="This is standard fivr loss model"/>
  </UpsType>

  <UpsType level="Block" config_name="FivrBlock_speed-discrete" type="FivrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="StdFivrLossModel" description="This is standard fivr loss model"/>
  </UpsType>

  <UpsType level="Block" config_name="FivrBlock_speed-discrete_ITD" type="FivrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="StdFivrLossModel" description="This is standard fivr loss model"/>
  </UpsType>

  <UpsType level="Block" config_name="FivrBlock_speed-const_dicc-conduct" type="FivrBlock">
    <Speed type="SpeedConstant" description="Speed constant model" />
    <Dicc type="DiccConductance" description="Dicc conductance*v model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="StdFivrLossModel" description="This is standard fivr loss model"/>
  </UpsType>

  <UpsType level="Block" config_name="FivrLoadLineBlock_ITD-2slope" type="FivrLoadLineBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="StdFivrLossModel" description="This is standard fivr loss model"/>
  </UpsType>


    <UpsType level="Block" config_name="DlvrBlockPsState_standard" type="DlvrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model"/>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="PsStateVdropModel" description="Ps State vdrop model"/>
  </UpsType>

    <UpsType level="Block" config_name="DlvrBlockPsState_ITD-2slope" type="DlvrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="PsStateVdropModel" description="Ps State vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockPsState_ITD" type="DlvrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="PsStateVdropModel" description="Ps State vdrop model"/>
  </UpsType>


  <UpsType level="Block" config_name="DlvrBlockPsState_AVX_ITD" type="DlvrBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="PsStateVdropModel" description="Ps State vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockPsState_AVX_ITD-2slope" type="DlvrBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="PsStateVdropModel" description="Ps State vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockPsState_speed-discrete_sicc-const" type="DlvrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccConstant" description="Sicc constant model"/>
    <Loss type="PsStateVdropModel" description="Ps State vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockPsState_speed-discrete" type="DlvrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="PsStateVdropModel" description="Ps State vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockPsState_speed-discrete_ITD" type="DlvrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="PsStateVdropModel" description="Ps State vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockPsState_speed-const_dicc-conduct" type="DlvrBlock">
    <Speed type="SpeedConstant" description="Speed constant model" />
    <Dicc type="DiccConductance" description="Dicc conductance*v model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="PsStateVdropModel" description="Ps State vdrop model"/>
  </UpsType>


      <UpsType level="Block" config_name="DlvrBlockLinear_standard" type="DlvrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model"/>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="LinearVdropModel" description="Linear vdrop model"/>
  </UpsType>

    <UpsType level="Block" config_name="DlvrBlockLinear_ITD-2slope" type="DlvrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="LinearVdropModel" description="Linear vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockLinear_ITD" type="DlvrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="LinearVdropModel" description="Linear vdrop model"/>
  </UpsType>


  <UpsType level="Block" config_name="DlvrBlockLinear_AVX_ITD" type="DlvrBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="LinearVdropModel" description="Linear vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockLinear_AVX_ITD-2slope" type="DlvrBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="LinearVdropModel" description="Linear vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockLinear_speed-discrete_sicc-const" type="DlvrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccConstant" description="Sicc constant model"/>
    <Loss type="LinearVdropModel" description="Linear vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockLinear_speed-discrete" type="DlvrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="LinearVdropModel" description="Linear vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockLinear_speed-discrete_ITD" type="DlvrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="LinearVdropModel" description="Linear vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrBlockLinear_speed-const_dicc-conduct" type="DlvrBlock">
    <Speed type="SpeedConstant" description="Speed constant model" />
    <Dicc type="DiccConductance" description="Dicc conductance*v model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="LinearVdropModel" description="Linear vdrop model"/>
  </UpsType>


  <UpsType level="Block" config_name="LdoBlock_standard" type="LdoBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model"/>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="LdoBlock_ITD" type="LdoBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="LdoBlock_speed-discrete_sicc-const" type="LdoBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccConstant" description="Sicc constant model"/>
  </UpsType>

  <UpsType level="Block" config_name="LdoBlock_speed-discrete" type="LdoBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="LdoBlock_speed-const_dicc-conduct" type="LdoBlock">
    <Speed type="SpeedConstant" description="Speed constant model" />
    <Dicc type="DiccConductance" description="Dicc conductance*v model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="LdoBlock_speed-avx" type="LdoBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model"/>
    <Dicc type="DiccCVFModel" description="Dicc conductance*v model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
  </UpsType>

  <UpsType level="Block" config_name="MockBlock1" type="MockBlock">
    <Speed type="MockModel" description="This is mock speed model" />
    <Dicc type="MockDiccModel" description="This is mock dicc model"/>
    <Sicc type="MockSiccModel" description="This is mock sicc model"/>
    <Loss type="MockFivrLossModel" description="This is mock fivr loss model"/>
  </UpsType>

    <UpsType level="Block" config_name="DlvrVinVoutQuad_standard" type="DlvrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model"/>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="VoutVinQuadVdrop" description="Vin vout quad vdrop model"/>
  </UpsType>

    <UpsType level="Block" config_name="DlvrVinVoutQuad_ITD-2slope" type="DlvrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="VoutVinQuadVdrop" description="Vin vout quad vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrVinVoutQuad_ITD" type="DlvrBlock">
    <Speed type="SpeedPiecewiseModel" description="Speed piecewise linear model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="VoutVinQuadVdrop" description="Vin vout quad vdrop model"/>
  </UpsType>


  <UpsType level="Block" config_name="DlvrVinVoutQuad_AVX_ITD" type="DlvrBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="VoutVinQuadVdrop" description="Vin vout quad vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrVinVoutQuad_AVX_ITD-2slope" type="DlvrBlock">
    <Speed type="MultiAvxModel" description="Speed multi AVX model">
      <ITD type="ItdTwoSlope" description="2-slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="VoutVinQuadVdrop" description="Vin vout quad vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrVinVoutQuad_speed-discrete_sicc-const" type="DlvrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccConstant" description="Sicc constant model"/>
    <Loss type="VoutVinQuadVdrop" description="Vin vout quad vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrVinVoutQuad_speed-discrete" type="DlvrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model" />
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="VoutVinQuadVdrop" description="Vin vout quad vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrVinVoutQuad_speed-discrete_ITD" type="DlvrBlock">
    <Speed type="SpeedDiscreteModel" description="Speed discrete model">
      <ITD type="ItdSlope" description="slope model for voltage and temperature scaling"/>
    </Speed>
    <Dicc type="DiccCVFModel" description="Dicc c*v*f model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="VoutVinQuadVdrop" description="Vin vout quad vdrop model"/>
  </UpsType>

  <UpsType level="Block" config_name="DlvrVinVoutQuad_speed-const_dicc-conduct" type="DlvrBlock">
    <Speed type="SpeedConstant" description="Speed constant model" />
    <Dicc type="DiccConductance" description="Dicc conductance*v model" />
    <Sicc type="SiccPETModel" description="Sicc pet model"/>
    <Loss type="VoutVinQuadVdrop" description="Vin vout quad vdrop model"/>
  </UpsType>
</UpsTypes>

