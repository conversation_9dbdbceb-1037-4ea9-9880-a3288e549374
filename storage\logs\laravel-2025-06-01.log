[2025-06-01 06:16:20] local.ERROR: Command "make:migrate" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:export
    make:factory
    make:import
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make:migrate\" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:export
    make:factory
    make:import
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view at C:\\Code\\bioe\\intel-fpga\\vendor\\symfony\\console\\Application.php:702)
[stacktrace]
#0 C:\\Code\\bioe\\intel-fpga\\vendor\\symfony\\console\\Application.php(258): Symfony\\Component\\Console\\Application->find()
#1 C:\\Code\\bioe\\intel-fpga\\vendor\\symfony\\console\\Application.php(167): Symfony\\Component\\Console\\Application->doRun()
#2 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#3 C:\\Code\\bioe\\intel-fpga\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-01 10:31:59] local.ERROR: Command "migrate:rollbcak" is not defined.

Did you mean one of these?
    migrate
    migrate:fresh
    migrate:install
    migrate:refresh
    migrate:reset
    migrate:rollback
    migrate:status {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"migrate:rollbcak\" is not defined.

Did you mean one of these?
    migrate
    migrate:fresh
    migrate:install
    migrate:refresh
    migrate:reset
    migrate:rollback
    migrate:status at C:\\Code\\bioe\\intel-fpga\\vendor\\symfony\\console\\Application.php:702)
[stacktrace]
#0 C:\\Code\\bioe\\intel-fpga\\vendor\\symfony\\console\\Application.php(258): Symfony\\Component\\Console\\Application->find()
#1 C:\\Code\\bioe\\intel-fpga\\vendor\\symfony\\console\\Application.php(167): Symfony\\Component\\Console\\Application->doRun()
#2 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#3 C:\\Code\\bioe\\intel-fpga\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 {main}
"} 
[2025-06-01 10:42:34] local.INFO: app:generate-line-item-table - createTable 'lineitem_1_1' successfully.  
[2025-06-01 10:42:34] local.INFO: app:generate-line-item-table - populateData lineitem_1_1 successfully.  
[2025-06-01 10:42:34] local.INFO: app:generate-line-item-table - Start createViewTable  
[2025-06-01 10:42:34] local.INFO: app:generate-line-item-table - createViewTable 'speed_1_1_1_2_0' successfully.  
[2025-06-01 10:42:34] local.INFO: app:generate-line-item-table - GenerateLineitemTable 1 completed.  
[2025-06-01 10:52:09] local.ERROR: Class "App\Http\Controllers\FuseManager" not found {"userId":1,"exception":"[object] (Error(code: 0): Class \"App\\Http\\Controllers\\FuseManager\" not found at C:\\Code\\bioe\\intel-fpga\\app\\Http\\Controllers\\SearchController.php:51)
[stacktrace]
#0 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\SearchController->getAttributesSelectorOptions()
#1 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction()
#2 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#3 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#4 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#6 C:\\Code\\bioe\\intel-fpga\\app\\Http\\Middleware\\RestrictModule.php(40): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#7 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictModule->handle()
#8 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#9 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets->handle()
#10 C:\\Code\\bioe\\intel-fpga\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#11 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Inertia\\Middleware->handle()
#12 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#13 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#14 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#15 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle()
#16 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#18 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#19 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#20 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#21 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#22 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle()
#23 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#24 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#25 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#27 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#29 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#30 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#31 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#32 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#33 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#34 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#36 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#37 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#38 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#39 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#40 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#41 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#42 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#44 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle()
#46 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#48 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Code\\bioe\\intel-fpga\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Code\\bioe\\intel-fpga\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#53 {main}
"} 
