<?php

namespace App\Http\Controllers;

use App\Http\Requests\FuseManagerUpdateRequest;
use App\Models\BinMatrix;
use App\Models\FuseManager;
use App\Models\Product;
use App\Models\ProductGroup;
use App\Models\ProductType;
use App\Services\GenerateFuseDefinitionTxtService;
use App\Services\GenerateSspecTxtService;
use App\Traits\WithProductData;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;

class FuseManagerContoller extends Controller
{
    use WithProductData;

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //Build Filter
        $filters = $this->filterSessions($request, 'fuse_config', [
            'keyword' => '',
            'product_id' => null,
            'product_group_id' => null,
            'bin_matrix_id' => null,
        ]);

        $list = FuseManager::query()->with(['productType', 'productGroup', 'product', 'createdUser'])
            ->accessibleBy(auth()->user())
            ->when(!empty($filters['keyword']), function ($q) use ($filters) {
                $q->orWhere('name', 'like', '%' . $filters['keyword'] . '%');
            })->when(!empty($filters['product_id']), function ($q) use ($filters) {
                $q->where('product_id', $filters['product_id']);
            })->when(!empty($filters['product_group_id']), function ($q) use ($filters) {
                $q->where('product_group_id', $filters['product_group_id']);
            })->when(!empty($filters['bin_matrix_id']), function ($q) use ($filters) {
                $q->where('bin_matrix_id', $filters['bin_matrix_id']);
            })
            ->filterSort($filters)
            ->orderBy('created_at', 'desc')->paginate(config('table.per_page'));

        $binMatrices = BinMatrix::select(['id', 'name', 'state', 'revision', 'product_group_id'])->get()->append('label');

        return Inertia::render('FuseManager/Index', $this->withProductData([
            'header' => FuseManager::header(),
            'filters' => $filters,
            'list' => $list,
            'binMatrices' => $binMatrices,
        ]));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return $this->edit(null);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(FuseManagerUpdateRequest $request)
    {
        return $this->update($request, null);
    }

    /**
     * Display the specified resource.
     */
    public function show(FuseManager $fuseManager)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(?FuseManager $fuseManager = null, bool $createRevision = false)
    {
        $productTypes = ProductType::select('name', 'id')->get();
        $products = Product::select('name', 'id', 'product_type_id')->get();
        $productGroups = ProductGroup::select('name', 'id', 'product_id')->get();
        $binMatrices = BinMatrix::select(['id', 'name', 'state', 'revision', 'product_group_id'])->get()->append('label');

        if (null === $fuseManager) {
            $data = new FuseManager;
        } else {
            //Clone for new revision 
            if ($createRevision) {
                $data = $fuseManager->load(['fuseConfigs', 'binMatrix']);
                //Increase revision
                $data['revision'] = FuseManager::getLatestRevision($fuseManager->original_id)?->revision + 1;

                //Reset value as new
                $data['approved'] = false;
                $data['created_user_id'] = null;
                $data['generated_at'] = null;
                foreach ($data->fuseConfigs as $index => $fuseConfigItem) {
                    $data->fuseConfigs[$index]['fuse_config_id'] = null;
                }
            } else {
                $data = $fuseManager->load(['fuseConfigs', 'createdUser']);
            }
        }

        return Inertia::render('FuseManager/Edit', [
            'data' => Inertia::always($data),
            'productTypes' => $productTypes,
            'products' => $products,
            'productGroups' => $productGroups,
            'binMatrices' => $binMatrices,
            'createRevision' => $createRevision,
            'states' => FuseManager::states(),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(FuseManagerUpdateRequest $request, ?FuseManager $fuseManager = null)
    {
        $data = $request->validated();

        if (null === $fuseManager) {
            $data['created_user_id'] = auth()->id();

            $fuseManager = FuseManager::create($data);
            return Redirect::route('fuse_managers.edit', $fuseManager->id)->with('message', 'Fuse Manager created successfully');
        } else {

            //Create new revision 
            if ($data['create_revision']) {
                $fuseManager = $fuseManager->createNewVersion($data);

                return Redirect::route('fuse_managers.edit', $fuseManager->id)->with('message', "Fuse Manager Revision {$fuseManager->revision} created successfully");
            } else {
            }

            return Redirect::route('fuse_managers.edit', $fuseManager->id)->with('message', 'Fuse Manager updated successfully');
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FuseManager $fuseManager)
    {
        // $fuseManager->fuseConfigs()->delete();
        $fuseManager->delete();
        return Redirect::route('fuse_managers.index')->with('message', 'Fuse Manager deleted successfully');
    }

    public function postState(Request $request, FuseManager $fuseManager)
    {
        $fuseManager->update(['state' => $request->state, 'state_update_user_id' => auth()->id(), 'state_updated_at' => now()]);
        return response()->json(['message' => 'State updated successfully']);
    }

    public function getRevisionCreate(FuseManager $fuseManager)
    {
        return $this->edit($fuseManager, true);
    }

    /**
     * Fuse Definition
     */
    public function getFuseDefinition(int $fuse_manager_id)
    {
        $service = new GenerateFuseDefinitionTxtService(fuse_manager_id: $fuse_manager_id);
        return $service->generateTxt();
    }

    public function getSSPEC(int $fuse_config_id)
    {
        $service = new GenerateSspecTxtService($fuse_config_id);
        return $service->generateTxt();
    }
}
