<?php

namespace App\Services;

use App\Helpers\FuseTextFile;
use App\Models\FuseConfig;

class GenerateFuseDefinitionTxtService
{
    protected $fuseConfig;
    protected $available_scenarios = [];
    protected $fuseManager;

    public function __construct(?int $fuse_manager_id = null, ?int $fuse_config_id = null)
    {
        if (!$fuse_config_id) {
            $this->fuseConfig = FuseConfig::with('fuseConfigItems', 'fuseConfigItems.fuseConfigItemAddresses', 'fuseManager')->findOrFail($fuse_config_id);
            $this->fuseManager = $this->fuseConfig->fuseManager;
        } else {
            $this->fuseManager = FuseManager::with('fuseConfigs.fuseConfigItems.fuseConfigItemAddresses')->findOrFail($fuse_manager_id);
        }
    }

    /**
     * Generate the Sspec export XML file
     * 
     * @return string The XML document as a string
     */
    public function generate()
    {
        $content = FuseTextFile::header($this->fuseManager);

        // Define column widths using collection methods
        $col1Width = 9;  // 'register:'
        $items = $this->fuseConfig->fuseConfigItems;

        // Get max lengths directly from the collection
        $col2Width = max($items->max(function ($item) {
            return strlen($item->register) + 1; // +1 for colon
        }), strlen('Register:'));

        $col3Width = max($items->max(function ($item) {
            return strlen($item->datatype) + 1; // +1 for colon
        }), strlen('Data Type:'));

        // Add register section header

        // Format string with fixed spacing between columns (4 spaces)
        $headerFormatStr = "#%-{$col1Width}s  %-{$col2Width}s  %-{$col3Width}s  %s\n";

        // Calculate total line length for separator
        $totalLength = 1 + // #
            $col1Width + 2 + // register: + spaces
            $col2Width + 2 + // Register: + spaces
            $col3Width + 2 + // Data Type: + spaces
            4; // Size

        $separator = str_repeat('#', $totalLength);

        // Add header with matching separator lines
        $content .= $separator . "\n";
        $content .= sprintf(
            $headerFormatStr,
            'register:',
            'Register:',
            'Data Type:',
            'Size'
        );
        $content .= $separator . "\n";


        $contentFormatStr = "%-{$col1Width}s   %-{$col2Width}s  %-{$col3Width}s  %s\n";
        // Add register entries with proper alignment
        foreach ($items as $item) {
            $content .= sprintf(
                $contentFormatStr,
                'register:',
                $item->register . ':',
                $item->datatype == "Hex" ? $item->datatype . "{2}:" : $item->datatype . ':',
                $item->size
            );
        }

        // Define column widths for the second format - adjust for actual data lengths
        $fusedefCol = max(strlen('#fusedef:'), 9);  // minimum 9 chars

        foreach ($items as $item) {
            // Calculate max lengths for register, address, setting and fuse_group for this item
            $maxRegisterLength = strlen('Register:');
            $maxAddressLength = strlen('Address:');
            $maxSettingLength = strlen('Setting:');
            $maxFuseGroupLength = strlen('Fuse Group:');

            // Calculate register length with colon
            $registerStr = $item->register . ':';
            $maxRegisterLength = max($maxRegisterLength, strlen($registerStr));

            foreach ($item->fuseConfigItemAddresses as $address) {
                $addressStr = $address->stop_address . '-' . $address->start_address . ':';
                $maxAddressLength = max($maxAddressLength, strlen($addressStr));

                $settingStr = $address->setting . ':';
                $maxSettingLength = max($maxSettingLength, strlen($settingStr));

                $fuseGroupStr = empty($address->fuse_group) ? $address->setting : $address->fuse_group;
                $maxFuseGroupLength = max($maxFuseGroupLength, strlen($fuseGroupStr));
            }

            $registerCol = $maxRegisterLength + 1;  // Add some padding
            $addressCol = $maxAddressLength + 1;  // Add some padding
            $fuseTypeCol = max(strlen('Fuse Type:'), 10);
            $settingCol = $maxSettingLength + 1;  // Add some padding
            $fuseGroupCol = $maxFuseGroupLength + 1;  // Add some padding

            // Create format string with proper spacing
            $addressFormatStr = "%-" . $fusedefCol . "s %-" . $registerCol . "s %-" . $addressCol . "s %-" . $fuseTypeCol . "s %-" . $settingCol . "s %s\n";

            // Calculate total length for separator
            $totalLength2 = $fusedefCol + $registerCol + $addressCol + $fuseTypeCol + $settingCol + $fuseGroupCol + 5; // +5 for spaces between columns

            // Add second header with matching separator
            $content .= "\n" . str_repeat('#', $totalLength2) . "\n";
            $content .= sprintf(
                $addressFormatStr,
                '#fusedef:',
                'Register:',
                'Address:',
                'Fuse Type:',
                'Setting:',
                'Fuse Group:'
            );
            $content .= str_repeat('#', $totalLength2) . "\n";

            foreach ($item->fuseConfigItemAddresses as $address) {
                $content .= sprintf(
                    $addressFormatStr,
                    'fusedef:',
                    $item->register . ':',
                    $address->stop_address . '-' . $address->start_address . ':',
                    'senr:',
                    $address->setting . ':',
                    empty($address->fuse_group) ? $address->setting : $address->fuse_group
                );
            }
        }

        // Define for Pin Type
        /**
         * HEADER
         * #########################################
         * #pin:  Pin Type:      Register:  Pin Name
         * ######################################### 
         */
        // Calculate max lengths for pin type section
        $pinCol = max(strlen('#pin:'), 6);
        $maxPinTypeLength = strlen('Pin Type:');
        $maxRegisterLength = strlen('Register:');
        $maxPinNameLength = 0;

        // Calculate max lengths across all items
        foreach ($items as $item) {
            // Calculate register length with colon
            $registerStr = $item->register . ':';
            $maxRegisterLength = max($maxRegisterLength, strlen($registerStr));

            // Check pin_to_modify and pin_to_read lengths
            if (isset($item->pin_to_modify)) {
                $maxPinTypeLength = max($maxPinTypeLength, strlen('pin_to_modify:'));
                $maxPinNameLength = max($maxPinNameLength, strlen($item->pin_to_modify));
            }

            if (isset($item->pin_to_read)) {
                $maxPinTypeLength = max($maxPinTypeLength, strlen('pin_to_read:'));
                $maxPinNameLength = max($maxPinNameLength, strlen($item->pin_to_read));
            }
        }

        // Set column widths with padding
        $pinTypeCol = $maxPinTypeLength + 1;
        $pinRegisterCol = $maxRegisterLength + 1;
        $pinNameCol = $maxPinNameLength + 1;

        // Create format string with proper spacing
        $pinFormatStr = "%-" . $pinCol . "s %-" . $pinTypeCol . "s %-" . $pinRegisterCol . "s %s\n";

        // Calculate total length for separator
        $totalPinLength = $pinCol + $pinTypeCol + $pinRegisterCol + $pinNameCol + 3; // +3 for spaces between columns

        // Add header with matching separator lines - ONLY ONCE OUTSIDE THE LOOP
        $content .= "\n" . str_repeat('#', $totalPinLength) . "\n";
        $content .= sprintf(
            $pinFormatStr,
            '#pin:',
            'Pin Type:',
            'Register:',
            'Pin Name'
        );
        $content .= str_repeat('#', $totalPinLength) . "\n";

        // Add pin entries for all items
        foreach ($items as $item) {
            if (isset($item->pin_to_modify)) {
                $content .= sprintf(
                    $pinFormatStr,
                    'pin:',
                    'pin_to_modify:',
                    $item->register . ':',
                    $item->pin_to_modify
                );
            }

            if (isset($item->pin_to_read)) {
                $content .= sprintf(
                    $pinFormatStr,
                    'pin:',
                    'pin_to_read:',
                    $item->register . ':',
                    $item->pin_to_read
                );
            }
        }

        return $content;
    }

    public function getFileName(): string
    {
        return "fusedef.txt";
    }

    public function generateTxt()
    {
        $content = $this->generate();
        $headers = [
            'Content-Type' => 'text/plain',
            'Content-Disposition' => "attachment; filename={$this->getFileName()}",
        ];
        return response($content, 200, $headers);
    }
}
