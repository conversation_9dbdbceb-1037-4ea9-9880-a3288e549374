<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fuse_manager_attributes', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->smallInteger('sequence')->default(0);
            $table->string('attribute_name')->nullable(); //From another table column
            $table->string('module')->nullable();
            $table->unsignedBigInteger('lineitem_attribute_id')->nullable(); //Can be null, some attribute_name are from speed column
            $table->unsignedBigInteger('fuse_manager_id');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fuse_manager_attributes');
    }
};
