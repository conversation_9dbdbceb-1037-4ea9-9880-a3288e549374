<script setup>
const scrollToTop = () => {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
};

const scrollToBottom = () => {
    window.scrollTo({
        top: document.body.scrollHeight,
        behavior: 'smooth'
    });
};
</script>

<template>
    <div class="floating-buttons">
        <button @click="scrollToTop" class="btn btn-sm btn-secondary" title="Scroll to top">
            <i class="bi bi-arrow-up"></i>
        </button>
        <button @click="scrollToBottom" class="btn btn-sm btn-secondary" title="Scroll to bottom">
            <i class="bi bi-arrow-down"></i>
        </button>
    </div>
</template>

<style scoped>
.floating-buttons {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 5px;
    z-index: 1000;
}

.floating-buttons button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.5;
    transition: opacity 0.3s;
    font-size: 0.75rem;
}

.floating-buttons button:hover {
    opacity: 0.8;
}
</style>