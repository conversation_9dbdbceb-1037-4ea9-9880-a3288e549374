<?php

namespace App\Console\Commands;

use App\Imports\SpeedImport;
use App\Models\GenerateBatchLog;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\HeadingRowImport;

class GenerateBatchTable extends BaseCommand
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-batch-table {speed_batch_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Dynamicially generate a new batch table';

    protected $speed_batch_id = null;
    protected $log = null;
    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->speed_batch_id = $this->argument('speed_batch_id');
        $this->start();
    }

    public function start()
    {
        $this->log = GenerateBatchLog::with(['speedBatch'])->find($this->speed_batch_id);
        if (!$this->log) {
            $this->updateFail('No Speed Batch found.');
            return;
        }

        $batch = $this->log->speedBatch;

        if (!$batch) {
            $this->updateFail('No Batch found.');
            return;
        } else if ($batch->state === STATE_SNAPSHOT) {
            $this->updateFail("Batch $batch->id has been SNAPSHOT, no longer able to generate.");
            return;
        }

        $newTable = $batch->generate_based_name;
        $files = Storage::disk('public')->path($batch->file_path);
        $headings = (new HeadingRowImport())->toArray($files);
        if (!isset($headings[0][0])) {
            $this->updateFail('No headings found.');
            return;
        }

        if (!$this->createTable($newTable, $headings[0][0])) return;

        $this->populateData($newTable, $files);

        //Update GenerateBatchLog
        $this->log->completed_at = now();
        $this->log->failed = false;
        $this->log->save();

        // $this->createViewTable($line);

        $this->updateMessage("GenerateBatchTable {$this->log->id} completed.");
    }


    private function createTable($newTable, $headers)
    {
        try {
            // Drop table if exists (PostgreSQL syntax)
            DB::statement("DROP TABLE IF EXISTS \"{$newTable}\" CASCADE");

            // Check if the new table already exists
            if (Schema::hasTable($newTable)) {
                $this->updateFail("Table '{$newTable}' already exists.");
                return false;
            }

            //Initialize default columns
            $defaultColumns = config('settings.speed_default_data_types');

            // Build CREATE TABLE statement
            $createSQL = "CREATE TABLE \"{$newTable}\" (";
            $columnDefinitions = [];

            //Primary Key
            $columnDefinitions[] = "\"id\" bigint GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY";

            // Use excel header to create database header
            if (isset($headers)) {
                //Skip column
                $exclude_columns = config('settings.exclude_columns');
                $headers = array_filter($headers, function($key) use ($exclude_columns) {
                    return !in_array($key, $exclude_columns) && !is_numeric($key);
                });

                foreach ($headers as $columnName) {
                    $definition = "\"{$columnName}\" ";

                    if (isset($defaultColumns[strtoupper($columnName)])) {
                        $dataType = $defaultColumns[strtoupper($columnName)];
                    } else {
                        $dataType = "varchar";
                    }

                    $definition .= databaseDataType($dataType);

                    $definition .= " NULL";
                    $columnDefinitions[] = $definition;
                }
            }
            $columnDefinitions[] = "\"created_at\" timestamp NULL";
            $columnDefinitions[] = "\"updated_at\" timestamp NULL";

            $createSQL .= implode(', ', $columnDefinitions) . ')';
            // Create the new table
            DB::statement($createSQL);

            $this->updateMessage("createTable '{$newTable}' successfully.");
            return true;
        } catch (\Exception $e) {
            $this->updateFail("createTable {$newTable} failed: " . $e->getMessage());
            return false;
        }
    }

    private function populateData($newTable, $filePath)
    {
        try {
            Excel::import(new SpeedImport($newTable, true, null), $filePath);
            $this->updateMessage("populateData {$newTable} successfully.");
        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $errors = excelFailureMessage($e);
            $this->updateFail("populateData {$newTable} failed: " . implode(', ', $errors));
            return false;
        } catch (\Exception $e) {
            $this->updateFail("populateData {$newTable} failed: " . $e->getMessage());
            return false;
        }
    }

    //Set to failed and record the message
    private function updateFail($message)
    {
        $this->log($message);
        $this->log->failed = true;
        $this->log->exception = $message;
        $this->log->save();
    }

    private function updateMessage($message)
    {
        $this->log($message);
        // Format message with timestamp and line break before appending
        $formattedMessage = "[" . now()->format('Y-m-d H:i:s') . "] " . $message . "\n";
        $this->log->messages .= $formattedMessage;
        $this->log->save();
    }
}
