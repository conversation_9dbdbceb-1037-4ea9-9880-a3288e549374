<script setup>
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import TextInput from '@/Components/TextInput.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';
import { Head, useForm, Link, router } from '@inertiajs/vue3';
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import axios from 'axios';
import ExpressionModal from '@/Components/ExpressionModal.vue';
import Component from './Component.vue';
import ComponentLevelModal from './ComponentLevelModal.vue';
import ProductSelectors from '@/Components/ProductSelectors.vue';
import AttributeMapModal from './AttributeMapModal.vue';
import ComponentModule from './ComponentModule.vue';
import Select from '@/Components/Select.vue';
import ExpressionSuggestions from '@/Components/ExpressionSuggestions.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    productTypes: {
        type: Object,
    },
    products: {
        type: Object,
    },
    productGroups: {
        type: Object,
    },
    binMatrixOptions: {
        type: Object,
    },
    scenarioOptions: {
        type: Object,
    },
    createRevision: {
        type: Boolean,
    },
    states: {
        type: Array,
    },
    columns: {
        type: Object,
    },
    expressionHtml: {
        type: String,
    },
});

const routeGroupName = 'usm';
const headerTitle = ref('Universal Spec Manager');

const localData = ref({ ...props.data });
const binMatrixOptions = ref(props.binMatrixOptions);

const activeTab = ref('#tab_1');

const setActiveTab = (tabId) => {
    activeTab.value = tabId;
    localStorage.setItem('activeTab', tabId);
};

const inertiaNavigationHandler = (event) => {
    const isLeavingEditPage = !event.detail.page.url.includes('/edit');
    if (isLeavingEditPage) {
        localStorage.removeItem('activeTab');
    }
};

onMounted(() => {
    const savedTab = localStorage.getItem('activeTab');
    if (savedTab) {
        activeTab.value = savedTab;
    }
    router.on('navigate', inertiaNavigationHandler);
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
});

onUnmounted(() => {
    router.on('navigate', () => { });
});

watch(
    () => props.data,
    newVal => {
        localData.value = { ...newVal };
    },
);

const form = useForm({
    create_revision: props.createRevision,
    revision: props.data.revision,
    name: props.data.name ?? null,
    product_type_id: props.data.product_type_id ?? null,
    product_id: props.data.product_id ?? null,
    product_group_id: props.data.product_group_id ?? null,
    bin_matrix_id: props.data.bin_matrix_id ?? null,
    fuse_manager_ids: props.data.fuse_manager_ids ?? [],
    fuse_config_items: props.data.fuse_config_items ?? [],
    modules: props.data.modules ?? [],
    scenarios: props.data.scenarios ?? [],
});

// Watch for product_group_id changes to fetch bin matrix options
watch(
    () => form.product_group_id,
    newVal => {
        if (newVal) {
            // Reset bin_matrix_id when product_group_id changes
            form.bin_matrix_id = null;
            binMatrixOptions.value = [];

            // Only fetch bin matrix options if all product selections are made
            if (form.product_type_id && form.product_id && form.product_group_id) {
                axios
                    .get(route('search.bin_matrix_options'), {
                        params: {
                            product_type_id: form.product_type_id,
                            product_id: form.product_id,
                            product_group_id: form.product_group_id,
                        },
                    })
                    .then(response => {
                        binMatrixOptions.value = response.data;
                    })
                    .catch(error => {
                        console.error('Error fetching bin matrix options:', error);
                    });
            }
        }
    },
);

// Also watch for product_type_id and product_id changes to reset bin_matrix_id
watch([() => form.product_type_id, () => form.product_id], () => {
    form.bin_matrix_id = null;
    binMatrixOptions.value = [];
});

// Handle modules
const addRow = () => {
    form.modules.push({
        name: '',
        keyword: '',
        sequence: form.modules.length + 1,
    });
};

const removeRow = index => {
    form.modules.splice(index, 1);
    //Reupdate the sequence
    form.modules.forEach((row, index) => {
        row.sequence = index + 1;
    });
};

// Handle scenarios
const addScenario = () => {
    form.scenarios.push({
        in_xml: true,
        name: '',
        sequence: form.scenarios.length + 1,
    });
};

const removeScenario = index => {
    form.scenarios.splice(index, 1);
    //Reupdate the sequence
    form.scenarios.forEach((row, index) => {
        row.sequence = index + 1;
    });
};

const isReadOnly = computed(() => {
    return props.createRevision ? true : false;
});

const editDisabled = computed(() => {
    return localData.value.state == 'SNAPSHOT';
});

const submit = () => {
    if (props.data.id == null) {
        form.post(route(routeGroupName + '.store'), {
            preserveState: false,
        });
    } else {
        form.patch(route(routeGroupName + '.update', props.data.id), { preserveState: false });
    }
};

const setState = state => {
    const c = confirm('Change the state to ' + state);
    if (c) {
        axios
            .post(route(routeGroupName + '.state.update', props.data.id), { state: state })
            .then(response => {
                localData.value.state = state;
                alert(response.data.message);
            })
            .catch(error => {
                alert(error.response.data.message);
            });
    }
};
</script>

<template>

    <Head :title="headerTitle" />

    <AuthenticatedLayout>
        <template #header> {{ headerTitle }} </template>
        <span v-if="createRevision">(New Revision {{ data.revision }})</span>

        <form @submit.prevent="submit">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <ul class="nav nav-tabs card-header-tabs">
                        <li class="nav-item">
                            <a class="nav-link" :class="{ active: activeTab === '#tab_1' }" data-bs-toggle="tab"
                                href="#tab_1" @click="setActiveTab('#tab_1')">Details</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" :class="{ active: activeTab === '#tab_2' }" data-bs-toggle="tab"
                                href="#tab_2" @click="setActiveTab('#tab_2')">Component Structure</a>
                        </li>
                        <li class="nav-item" v-for="(module, index) in data.modules" :key="index">
                            <a class="nav-link" v-if="module.id != null"
                                :class="{ active: activeTab === ('#tab_' + (index + 3)) }" data-bs-toggle="tab"
                                :href="'#tab_' + (index + 3)" @click="setActiveTab('#tab_' + (index + 3))">{{
                                    module.keyword
                                }}</a>
                        </li>
                    </ul>
                    <div v-if="data.id != null && createRevision == false" class="d-flex gap-2">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false"><i class="bi bi-check-circle"></i> {{ localData.state }}</button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li v-for="state in states" :key="state">
                                    <button type="button" class="dropdown-item" @click="setState(state)"
                                        :disabled="state == localData.state">{{ state }}</button>
                                </li>
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                <i class="bi bi-three-dots-vertical"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li>
                                    <button type="button" class="dropdown-item" data-bs-toggle="modal"
                                        data-bs-target="#componentLevelModal">Component Levels</button>
                                </li>
                                <li>
                                    <button type="button" class="dropdown-item" data-bs-toggle="modal"
                                        data-bs-target="#attributeMapModal">Template Attribute Map</button>
                                </li>

                                <!-- <li>
                                    <hr class="dropdown-divider" />
                                </li> 
                                <li>
                                    <Link class="dropdown-item" :href="route(routeGroupName + '.revision.create', data.id)"> Create New Revision</Link>
                                </li> -->
                            </ul>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-info dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                aria-expanded="false">
                                <i class="bi bi-file-earmark-arrow-down"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu">
                                <li>
                                    <a class="dropdown-item"
                                        :href="route(routeGroupName + '.export_lineitemdata_xml', data.id)">Export
                                        LineItemData
                                        XML ZIP</a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                        :href="route(routeGroupName + '.export_usm_xml', data.id)">Export
                                        USM XML</a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                        :href="route(routeGroupName + '.export_structure_xml', data.id)">Export
                                        Structure
                                        XML</a>
                                </li>
                                <li>
                                    <a class="dropdown-item"
                                        :href="route(routeGroupName + '.export_ups_xml', data.id)">Export
                                        UPS XML ZIP</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="tab-content">
                        <div class="tab-pane fade pt-10"
                            :class="{ show: activeTab === '#tab_1', active: activeTab === '#tab_1' }" id="tab_1"
                            role="tabpanel" aria-labelledby="tab_1">
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <InputLabel for="name" value="Name" />
                                    <TextInput id="name" type="text" v-model="form.name" :invalid="form.errors.name"
                                        :readonly="isReadOnly" required />
                                    <InputError :message="form.errors.name" />
                                </div>

                                <ProductSelectors v-model="form" :product-types="productTypes" :products="products"
                                    :product-groups="productGroups" :errors="form.errors" />

                                <div class="col-md-3">
                                    <InputLabel for="bin_matrix" value="Bin Matrix" />
                                    <Select id="bin_matrix" v-model="form.bin_matrix_id" :options="binMatrixOptions"
                                        :invalid="form.errors.bin_matrix_id" required />
                                    <InputError :message="form.errors.bin_matrix_id" />
                                </div>
                            </div>

                            <div class="row g-3 mt-2">
                                <div class="col-12">
                                    <div class="text-end">
                                        <!-- Revision: {{ data.revision }} <br /> -->
                                        <template v-if="data.created_user">Created By: {{ data.created_user.name }}
                                        </template>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-3 mt-3">
                                <!-- Add Module Table -->
                                <div class="col-md-6">
                                    <h4>Modules</h4>
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th width="3%">
                                                    <button type="button" class="btn btn-sm btn-primary m-1"
                                                        @click="addRow" title="Add Module"><i
                                                            class="bi bi-plus"></i></button>
                                                </th>
                                                <th width="5%">No</th>
                                                <th>Module Name</th>
                                                <th>Keyword</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(row, index) in form.modules" :key="index">
                                                <td class="text-center">
                                                    <button type="button" class="btn btn-sm btn-danger m-1"
                                                        @click="removeRow(index)" title="Remove"><i
                                                            class="bi bi-dash"></i></button>
                                                </td>
                                                <td class="text-center">{{ index + 1 }}</td>
                                                <td>
                                                    <TextInput type="text" v-model="row.name" />
                                                </td>
                                                <td>
                                                    <TextInput type="text" v-model="row.keyword" />
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h4>Scenarios</h4>
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th width="3%">
                                                    <button type="button" class="btn btn-sm btn-primary m-1"
                                                        @click="addScenario" title="Add Scenario"><i
                                                            class="bi bi-plus"></i></button>
                                                </th>
                                                <th width="5%">No</th>
                                                <th>In XML</th>
                                                <th>Name</th>
                                                <th>Expression</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(row, index) in form.scenarios" :key="index">
                                                <td class="text-center">
                                                    <button type="button" class="btn btn-sm btn-danger m-1"
                                                        @click="removeScenario(index)" title="Remove"><i
                                                            class="bi bi-dash"></i></button>
                                                </td>
                                                <td class="text-center">{{ index + 1 }}</td>
                                                <td>
                                                    <input type="checkbox" class="form-check-input"
                                                        v-model="row.in_xml" />
                                                </td>
                                                <td>
                                                    <TextInput type="text" v-model="row.name" />
                                                </td>
                                                <td>
                                                    <ExpressionSuggestions :suggestions="columns"
                                                        v-model="row.expression" :placeholder="''" />
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="row g-3 mt-3" v-if="data.bin_matrix_id">
                                <div class="col-md-6">
                                    <h4>Fuse Configurations</h4>

                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th width="10%"></th>
                                                <th>Name</th>
                                                <th class="text-center" width="10%">Revision</th>
                                                <th class="text-center" width="15%">State</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(row, index) in data.bin_matrix.fuse_managers" :key="index">
                                                <td class="text-center">
                                                    <input type="checkbox" class="form-check-input"
                                                        v-model="form.fuse_manager_ids" :value="row.id" />
                                                </td>
                                                <td>{{ row.name }}</td>
                                                <td class="text-center">{{ row.revision }}</td>
                                                <td class="text-center">{{ row.state }}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="col-12">
                                <PrimaryButton v-if="!editDisabled" type="submit"
                                    v-html="data.id == null ? 'Create' : 'Save'" :disabled="form.processing">
                                </PrimaryButton>
                            </div>
                        </div>
                        <div v-if="data.id" class="tab-pane fade pt-10"
                            :class="{ show: activeTab === '#tab_2', active: activeTab === '#tab_2' }" id="tab_2"
                            role="tabpanel" aria-labelledby="tab_2">
                            <Component :detailForm="form" :usm_id="data.id" />
                        </div>
                        <div v-if="data.id" v-for="(module, index) in data.modules" :key="index"
                            class="tab-pane fade pt-10"
                            :class="{ show: activeTab === '#tab_' + (index + 3), active: activeTab === '#tab_' + (index + 3) }"
                            :id="'tab_' + (index + 3)" role="tabpanel" :aria-labelledby="'tab_' + (index + 3)">
                            <ComponentModule :usm_module_id="module.id" :usm_id="data.id"
                                :scenarioOptions="scenarioOptions" :columns="columns"
                                :scenario_mode="module.scenario_mode" />
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex">
                        <div class="me-auto">
                            <Link class="btn btn-secondary me-2" :href="route(routeGroupName + '.index')">Back</Link>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <ExpressionModal />
        <ComponentLevelModal :usm_id="localData.id" />
        <AttributeMapModal :usm_id="localData.id" />
    </AuthenticatedLayout>
</template>
