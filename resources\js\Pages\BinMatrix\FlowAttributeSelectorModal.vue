<script setup>
import FlashAlert from '@/Components/FlashAlert.vue';
import Modal from '@/Components/Modal.vue';
import Select from '@/Components/Select.vue';
import { useForm } from '@inertiajs/vue3';
import { onMounted, ref, computed } from 'vue';

const flowAttributeSelectorModal = ref(null);
const leftItems = ref([]);
const rightItems = ref([]);
const moduleOptions = ref([]);
const selectedModule = ref(null);
const attributesData = ref({});
const originalData = ref({}); // Store original data before filtering

// Define emits
const emit = defineEmits(['saveSuccess']);

//bin_matrix_id or fuse_manager_id either one
const props = defineProps({
    bin_matrix_id: {
        type: Number,
    },
    fuse_manager_id: {
        type: Number,
    },
    editDisabled: {
        type: Boolean,
        default: false,
    },
});

const form = useForm({
    rows: [],
});

onMounted(() => {
    const modalElement = document.getElementById('flowAttributeSelectorModal');
    modalElement.addEventListener('show.bs.modal', () => {
        axios.get(route('search.attributes_selector_options', { fuse_manager_id: props.fuse_manager_id, bin_matrix_id: props.bin_matrix_id })).then(response => {
            // Convert modules object to array of options
            moduleOptions.value = Object.entries(response.data.modules).map(([key, value]) => ({
                id: key,
                name: value,
            }));

            // Store original data
            originalData.value = { ...response.data };

            // Initialize right items from the response
            rightItems.value = response.data.right;

            // Create a deep copy of the left items to avoid reference issues
            const filteredLeft = JSON.parse(JSON.stringify(response.data.left));

            // Filter out items from left that exist in right
            rightItems.value.forEach(rightItem => {
                if (filteredLeft[rightItem.module]) {
                    filteredLeft[rightItem.module] = filteredLeft[rightItem.module].filter(
                        leftItem => leftItem.attribute !== rightItem.attribute_name
                    );
                }
            });

            // Store the filtered attributes data
            attributesData.value = {
                ...response.data,
                left: filteredLeft,
            };

            // Set default module if available
            if (moduleOptions.value.length > 0) {
                selectedModule.value = moduleOptions.value[0].id;
                updateLeftItems(selectedModule.value);
            }
        });
    });
});

const updateLeftItems = moduleKey => {
    if (attributesData.value.left && attributesData.value.left[moduleKey]) {
        // Get all items from the module
        const allItems = attributesData.value.left[moduleKey].map((item, index) => ({
            id: null,
            lineitem_attribute_id: item.id,
            module: moduleKey,
            name: item.attribute,
            attribute_name: item.attribute,
            order: index,
            sequence: index,
        }));

        leftItems.value = allItems;
    } else {
        // If no items for this module, set to empty array
        leftItems.value = [];
    }
};

const handleModuleChange = value => {
    selectedModule.value = value;
    updateLeftItems(value);
};

const save = () => {
    form.rows = rightItems.value;

    let routeName = 'bin_matrix.flow_attributes.update';
    let id = props.bin_matrix_id;
    if (props.fuse_manager_id != null) {
        routeName = 'fuse_managers.header_attributes.update';
        id = props.fuse_manager_id;
    }

    form.post(route(routeName, id), {
        preserveScroll: true,
        onSuccess: () => {
            emit('saveSuccess');
            closeModal();
        }
    });
};

const closeModal = () => {
    flowAttributeSelectorModal.value.close();
};

const transferItemToRight = item => {
    const index = leftItems.value.indexOf(item);
    if (index > -1) {
        // Remove from left items array
        leftItems.value.splice(index, 1);

        // Also remove from attributesData to keep data in sync
        if (attributesData.value.left[item.module]) {
            const attrIndex = attributesData.value.left[item.module].findIndex(
                leftItem => leftItem.attribute === item.attribute_name
            );
            if (attrIndex > -1) {
                attributesData.value.left[item.module].splice(attrIndex, 1);
            }
        }

        // Add to right items
        rightItems.value.push(item);

        // Recalculate sequence for all items in right panel
        rightItems.value.forEach((item, index) => {
            item.sequence = index;
        });
    }
};

const transferItemToLeft = item => {
    const index = rightItems.value.indexOf(item);
    if (index > -1) {
        // Remove the item from the right side
        rightItems.value.splice(index, 1);

        // Recalculate sequence for remaining items in right panel
        rightItems.value.forEach((item, index) => {
            item.sequence = index;
        });

        // Add the item back to attributesData.left
        if (!attributesData.value.left[item.module]) {
            attributesData.value.left[item.module] = [];
        }

        // Create a new left item with the correct format
        const newLeftItem = {
            id: item.lineitem_attribute_id || null,
            attribute: item.attribute_name
        };

        // Add the item to the left side data
        attributesData.value.left[item.module].push(newLeftItem);

        // Sort based on original order if possible
        if (originalData.value.left && originalData.value.left[item.module]) {
            const originalItems = originalData.value.left[item.module];

            // Sort the items based on their position in the original data
            attributesData.value.left[item.module].sort((a, b) => {
                const aIndex = originalItems.findIndex(origItem => origItem.attribute === a.attribute);
                const bIndex = originalItems.findIndex(origItem => origItem.attribute === b.attribute);

                // If item is not found in original data, put it at the end
                if (aIndex === -1) return 1;
                if (bIndex === -1) return -1;

                return aIndex - bIndex;
            });
        }

        // Only update left items if we're currently viewing the same module
        if (selectedModule.value === item.module) {
            updateLeftItems(selectedModule.value);
        }
    }
};

//Computed
const buttonYes = computed(() => {
    return props.editDisabled ? null : 'Save';
});
</script>

<template>
    <Modal ref="flowAttributeSelectorModal" @yesEvent="save" @noEvent="closeModal" :id="'flowAttributeSelectorModal'"
        :title="'Attributes Selector'" :buttonYes="buttonYes" :buttonType="'primary'" :form="form"
        :modalClass="'modal-lg'">
        <FlashAlert v-if="Object.keys(form.errors).length && !form.errors.file" :status="'danger'"
            @close="form.clearErrors()">
            <label v-for="(message, field) in form.errors" :key="field">
                {{ message }}
            </label>
        </FlashAlert>

        <div class="row mb-3">
            <label for="modules" class="col-sm-1 col-form-label">Module:</label>
            <div class="col-sm-5">
                <Select id="modules" :options="moduleOptions" v-model="selectedModule"
                    @update:modelValue="handleModuleChange" placeholder="Select a module"> </Select>
            </div>
        </div>

        <div class="row">
            <!-- Left Table -->
            <div class="col-6">
                <div class="table-wrapper" style="height: 300px; overflow-y: auto">
                    <table class="table table-bordered table-hover">
                        <thead class="sticky-top bg-white">
                            <tr>
                                <th scope="col">Available Attributes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in leftItems" :key="item.order" @dblclick="transferItemToRight(item)"
                                style="cursor: pointer">
                                <td>{{ item.name }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- Right Table -->
            <div class="col-6">
                <div class="table-wrapper" style="height: 300px; overflow-y: auto">
                    <table class="table table-bordered table-hover">
                        <thead class="sticky-top bg-white">
                            <tr>
                                <th scope="col">Module</th>
                                <th scope="col">Selected Attributes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="item in rightItems" :key="item.order" @dblclick="transferItemToLeft(item)"
                                style="cursor: pointer">
                                <td>{{ item.module }}</td>
                                <td>{{ item.name }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <small>
            <i>Double click to add and remove attributes</i>
        </small>
    </Modal>
</template>

<style scoped>
.table-wrapper {
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.sticky-top {
    z-index: 1;
}
</style>
